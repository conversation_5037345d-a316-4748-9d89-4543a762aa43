/**
 * API Module Index
 * Central export point for all API functionality
 */

// Core API infrastructure
export { BaseApiClient } from './base-client';
export { AuthManager, getAuthToken, isTokenExpired } from './auth-manager';
export { interceptorManager } from './interceptors';
export { ApiErrorHandler, handleApiError, handleAuthApiError, handleOnboardingApiError, handleCategoriesApiError } from './errors';
export { API_CONFIG, API_ENDPOINTS, HTTP_STATUS, AUTH_REDIRECTS, DEFAULT_HEADERS } from './config';

// Import classes for utility functions
import { BaseApiClient } from './base-client';
import { AuthManager } from './auth-manager';
import { interceptorManager } from './interceptors';
import { ApiErrorHandler } from './errors';
import type { HealthCheckResponse } from './types';

// API types
export type {
  ApiRequestConfig as RequestConfig,
  BaseApiResponse as ApiResponse,
  BaseApiResponse,
  PaginationParams,
  PaginatedResponse,
  ApiRequestConfig,
  FileUploadConfig,
  UploadProgress,
  LoginRequest,
  RegisterRequest,
  ForgotPasswordRequest,
  ResetPasswordRequest,
  ChangePasswordRequest,
  ResendVerificationRequest,
  BusinessInfoRequest,
  PaymentInfoRequest,
  ShopInfoRequest,
  ShippingInfoRequest,
  CategoryRequest,
  SearchParams,
  BulkOperationRequest,
  BulkOperationResponse,
  WebhookPayload,
  CacheConfig,
  RateLimitInfo,
  ApiVersion,
  HealthCheckResponse,
  ApiMetrics,
} from './types';

// Error types
export type {
  ApiError,
  ValidationError,
  NetworkError,
  AuthenticationError,
} from './errors';

// Interceptor types
export type {
  InterceptorContext,
  ResponseInterceptor,
} from './interceptors';

// Auth manager types
export type {
  TokenInfo,
  SessionInfo,
} from './auth-manager';

// API modules
export { authApi } from './auth';
export { onboardingApi } from './onboarding';
export { categoriesApi } from './categories';
export { profileApi } from './profile';
export { productsApi } from './products';
export { buyerProductsApi } from './buyer-products';

// Re-export domain types for convenience
export type {
  User,
  UserRole,
  AuthState,
  LoginCredentials,
  RegisterCredentials,
  AuthResponse,
  ForgotPasswordCredentials,
  ForgotPasswordResponse,
  ResetPasswordCredentials,
  ResetPasswordResponse,
  ChangePasswordCredentials,
  EmailVerificationResponse,
  ResendVerificationCredentials,
  ResendVerificationResponse,
  RegistrationResponse,
  BusinessInfoResponse,
  BusinessInfoUpdateData,
  OnboardingStatusResponse,
  BusinessAddress,
  BusinessInfo,
  OnboardingProgress,
  VerificationDetails,
} from '@/lib/types/auth';

export type {
  Category,
  CategoriesHierarchyResponse,
  CategoriesState,
  FlattenedCategory,
  NavigationCategory,
  NavigationLink,
} from '@/lib/types/categories';

export type {
  Product,
  ProductVariation,
  ProductSpecifications,
  CreateProductData,
  CreateProductResponse,
  ProductInfoFormData,
  SpecificationsFormData,
  VariationsFormData,
  ProductFormData,
} from '@/lib/types/products';

export type {
  BuyerProductsQueryParams,
  ProductItem,
  BuyerProductsResponse,
  ProductDetailResponse,
} from './buyer-products';

// Utility functions
export const createApiClient = (baseURL?: string) => new BaseApiClient(baseURL);

// API health check utility
export const checkApiHealth = async (): Promise<HealthCheckResponse> => {
  const client = new BaseApiClient();
  try {
    return await client.publicGet<HealthCheckResponse>('/health');
  } catch (error) {
    return {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      version: 'unknown',
      services: {
        api: {
          status: 'down',
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      },
    };
  }
};

// Performance monitoring utilities
export const getApiMetrics = () => interceptorManager.getPerformanceMetrics();
export const clearApiMetrics = () => interceptorManager.clearPerformanceMetrics();
export const enableApiLogging = (enabled: boolean) => interceptorManager.setLogging(enabled);

// Session management utilities
export const getSessionInfo = () => AuthManager.getSessionInfo();
export const clearSession = () => AuthManager.clearAuth();
export const hasRole = (role: 'buyer' | 'creator') => AuthManager.hasRole(role);
export const getDashboardUrl = (role?: 'buyer' | 'creator') => AuthManager.getDashboardUrl(role);

// Token validation utilities
export const isValidToken = (token: string) => AuthManager.isValidTokenFormat(token);
export const needsTokenRefresh = () => AuthManager.needsRefresh();

// Error handling utilities
export const isNetworkError = (error: any) => ApiErrorHandler.isNetworkError(error);
export const isAuthError = (error: any) => ApiErrorHandler.isAuthenticationError(error);
export const isValidationError = (error: any) => ApiErrorHandler.isValidationError(error);
export const getUserErrorMessage = (error: any) => ApiErrorHandler.getUserMessage(error);

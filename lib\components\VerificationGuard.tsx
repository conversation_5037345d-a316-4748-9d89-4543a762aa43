'use client';

import React from 'react';
import { useVerificationStatus } from '@/lib/hooks/use-onboarding';
import VerificationPrompt from '@/components/creators/VerificationPrompt';
import LottieLoader from '@/components/ui/LottieLoader';

interface VerificationGuardProps {
  children: React.ReactNode;
  feature: 'products' | 'orders' | 'earnings' | 'promotions' | 'bales' | 'feedback' | 'general';
  title?: string;
  description?: string;
}

const VerificationGuard: React.FC<VerificationGuardProps> = ({ 
  children, 
  feature,
  title,
  description 
}) => {
  const { data: verificationResponse, isLoading, error } = useVerificationStatus();
  const verificationData = verificationResponse?.data;
  const isVerified = verificationData?.verificationStatus === 'verified';

  // Handle different verification statuses
  const verificationStatus = verificationData?.verificationStatus || 'unverified';

  // Show loading state
  if (isLoading) {
    return (
      <div className="w-full mt-16 py-4 flex items-center justify-center">
        <LottieLoader
          size="lg"
          text="Checking verification status..."
          textSize="md"
          centered={true}
        />
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="w-full mt-16 py-4 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-sm border border-red-200 p-6 text-center max-w-md">
          <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-red-50 flex items-center justify-center">
            <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Unable to Check Verification</h3>
          <p className="text-gray-600 mb-4">We couldn't verify your account status. Please try again.</p>
          <button 
            onClick={() => window.location.reload()} 
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // If verified, render children
  if (isVerified) {
    return <>{children}</>;
  }

  // If not verified, show verification prompt
  return (
    <VerificationPrompt
      type={feature}
      verificationStatus={verificationStatus}
      title={title}
      description={description}
    />
  );
};

export default VerificationGuard;

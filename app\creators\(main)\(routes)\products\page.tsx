"use client";
import React, { useState } from "react";
import { AiOutlineDelete } from "react-icons/ai";
import { Package } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import Image from "next/image";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import CustomTabsTrigger from "@/components/ui/custom/CustomTabsTrigger";
import { useRouter } from "next/navigation";
import { useCreatorProducts, useDeleteProduct, useProductCounts } from "@/lib/hooks/use-products";
import { Product, Bale, ProductType, ProductOrBale } from "@/lib/types/products";
import { Input } from "@/components/ui/input";
import VerificationGuard from "@/lib/components/VerificationGuard";
import { useVerificationStatus } from "@/lib/hooks/use-onboarding";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";


const ProductsPage = () => {
  const [activeTab, setActiveTab] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedType, setSelectedType] = useState<ProductType | "all">("all");
  const router = useRouter();

  // Check verification status first
  const { data: verificationResponse } = useVerificationStatus();
  const verificationData = verificationResponse?.data;
  const isVerified = verificationData?.verificationStatus === 'verified';

  // Build query parameters based on current filters
  const queryParams = React.useMemo(() => {
    const params: any = {
      page: currentPage,
      limit: 20,
      sort: '-createdAt',
    };

    if (searchTerm.trim()) {
      params.search = searchTerm.trim();
    }

    // Add type filtering
    if (selectedType !== "all") {
      params.type = selectedType;
    }

    if (activeTab !== "all") {
      switch (activeTab) {
        case "active":
          params.status = "active";
          break;
        case "pending":
          params.status = "pending";
          break;
        case "rejected":
          params.status = "rejected";
          break;
        case "draft":
          params.status = "draft";
          break;
      }
    }

    return params;
  }, [activeTab, searchTerm, currentPage, selectedType]);

  // Only make API calls if user is verified
  const { data: productsResponse, isLoading, error } = useCreatorProducts(
    queryParams,
    { enabled: isVerified }
  );
  const { data: countsResponse } = useProductCounts(
    selectedType !== "all" ? { type: selectedType } : undefined,
    { enabled: isVerified }
  );
  const deleteProductMutation = useDeleteProduct();

  const products = productsResponse?.data?.products || [];
  const totalProducts = productsResponse?.total || 0;
  const totalPages = Math.ceil(totalProducts / (queryParams.limit || 20));
  const counts = countsResponse?.data || {
    all: 0,
    active: 0,
    pending: 0,
    rejected: 0,
    lowStock: 0,
    outOfStock: 0,
  };

  // Client-side filtering for stock-based filters (not supported by API)
  const filteredProducts = products.filter((item: ProductOrBale) => {
    if (activeTab === "low-stock") return item.totalStock > 0 && item.totalStock <= 10;
    if (activeTab === "out-of-stock") return item.totalStock === 0;
    return true; // Other filters are handled server-side
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleDeleteProduct = async (productId: string, productName: string) => {
    if (window.confirm(`Are you sure you want to delete "${productName}"? This action cannot be undone.`)) {
      try {
        await deleteProductMutation.mutateAsync(productId);
      } catch (error) {
        // Error is handled by the mutation
      }
    }
  };

  const viewDetails = (id: string) => router.push(`/creators/products/${id}`);

  return (
    <VerificationGuard feature="products">
      <section className="w-full max-w-md mx-auto py-4">
    {/* Header */}
    <div className="flex justify-between items-center mb-4">
        <h1 className="text-sm font-bold uppercase text-gray-600">Manage Products</h1>
        <Link
          href="/creators/products/add?step=product-info"
          className="bg-primary text-primary-foreground text-sm px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors duration-200"
        >
          Add Product
        </Link>
      </div>

    <section className="w-full mb-8 space-y-4">
      {/* Type Filter */}
      <div className="flex items-center justify-between gap-4">
        <label className="text-sm font-medium text-gray-700">Filter by Type:</label>
        <Select value={selectedType} onValueChange={(value: ProductType | "all") => {
          setSelectedType(value);
          setCurrentPage(1); // Reset to first page when changing type
        }}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Select type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="product">Products</SelectItem>
            <SelectItem value="bale">Bales</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Search */}
      <Input
        placeholder="Search by name, description, or brand..."
        value={searchTerm}
        onChange={(e) => {
          setSearchTerm(e.target.value);
          setCurrentPage(1); // Reset to first page when searching
        }}
        className="w-full"
      />
    </section>


      {/* Filter Tabs */}
      <Tabs defaultValue="all" onValueChange={(value) => setActiveTab(value)} className="mb-6">
        <TabsList className="bg-gray-100  w-[98vw] overflow-x-scroll flex space-x-3 mb-2 p-2 justify-around">
          <CustomTabsTrigger value="all">All ({counts.all})</CustomTabsTrigger>
          <CustomTabsTrigger value="active">Approved ({counts.active})</CustomTabsTrigger>
          <CustomTabsTrigger value="pending">Pending ({counts.pending})</CustomTabsTrigger>
          <CustomTabsTrigger value="rejected">Rejected ({counts.rejected})</CustomTabsTrigger>
          <CustomTabsTrigger value="low-stock">Low Stock ({counts.lowStock})</CustomTabsTrigger>
          <CustomTabsTrigger value="out-of-stock">Out of Stock ({counts.outOfStock})</CustomTabsTrigger>
        </TabsList>
      </Tabs>

      {isLoading ? (
        <div className="bg-white shadow-sm rounded-lg overflow-hidden">
          <div className="p-8 text-center">
            <Package className="w-12 h-12 text-gray-400 mx-auto mb-4 animate-pulse" />
            <p className="text-gray-500">Loading products...</p>
          </div>
        </div>
      ) : error ? (
        <div className="bg-white shadow-sm rounded-lg overflow-hidden">
          <div className="p-8 text-center">
            <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 mb-4">Failed to load products</p>
            <Button onClick={() => window.location.reload()}>Try Again</Button>
          </div>
        </div>
      ) : (
        <div className="bg-white shadow-sm rounded-lg overflow-hidden">
          <Table className="">
            <TableHeader>
              <TableRow>
                <TableHead>Item</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Brand/Country</TableHead>
                <TableHead>Price</TableHead>
                <TableHead>Stock</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-center">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredProducts.length > 0 ? (
                filteredProducts.map((item: ProductOrBale) => (
                  <TableRow key={item._id} className="whitespace-nowrap">
                    {/* Item Column with Image & Name */}
                    <TableCell className="flex items-center space-x-2 max-w-[250px]" onClick={() => viewDetails(item._id)}>
                      {item.images && item.images.length > 0 ? (
                        <div className="w-10 h-10 rounded overflow-hidden relative">
                          <Image
                            src={item.images[0]}
                            alt={item.name}
                            fill
                            sizes="40px"
                            className="object-cover"
                            onError={() => {
                              console.error('Item image failed to load:', item.images[0]);
                            }}
                          />
                        </div>
                      ) : (
                        <div className="w-10 h-10 rounded bg-gray-100 flex items-center justify-center">
                          <Package className="w-5 h-5 text-gray-400" />
                        </div>
                      )}
                      <span className="truncate max-w-[230px] text-sm leading-tight">
                        {item.name}
                      </span>
                    </TableCell>

                    {/* Type */}
                    <TableCell className="text-sm text-gray-700">
                      <Badge variant="outline" className={item.type === 'product' ? 'bg-blue-50 text-blue-700' : 'bg-purple-50 text-purple-700'}>
                        {item.type === 'product' ? 'Product' : 'Bale'}
                      </Badge>
                    </TableCell>

                    {/* Brand/Country */}
                    <TableCell className="text-sm text-gray-700">
                      {item.type === 'product' ? (item as Product).brand : (item as Bale).country}
                    </TableCell>

                    {/* Price */}
                    <TableCell className="text-sm font-medium">GHS {item.basePrice}</TableCell>

                    {/* Stock */}
                    <TableCell className="text-sm">{item.totalStock}</TableCell>

                    {/* Status */}
                    <TableCell>
                      <Badge className={getStatusColor(item.status)}>
                        {item.status}
                      </Badge>
                    </TableCell>

                    {/* Actions */}
                    <TableCell className="w-auto whitespace-nowrap space-x-2">
                      <section className="flex items-center gap-2">
                        <Button
                          aria-label={`View ${item.type === 'product' ? 'Product' : 'Bale'}`}
                          className="bg-primary hover:bg-primary/90"
                          size="sm"
                          onClick={() => viewDetails(item._id)}
                        >
                          View
                        </Button>
                        <Button
                          variant="destructive"
                          aria-label={`Delete ${item.type === 'product' ? 'Product' : 'Bale'}`}
                          size="sm"
                          onClick={() => handleDeleteProduct(item._id, item.name)}
                          disabled={deleteProductMutation.isPending}
                        >
                          <AiOutlineDelete className="text-white" size={16} />
                        </Button>
                      </section>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500 mb-4">No products found for this filter</p>
                    {activeTab === "all" && (
                      <Link href="/creators/products/add?step=product-info">
                        <Button>Add Your First Product</Button>
                      </Link>
                    )}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center items-center gap-2 mt-6">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={currentPage === 1}
          >
            Previous
          </Button>

          <span className="text-sm text-gray-600">
            Page {currentPage} of {totalPages} ({totalProducts} total)
          </span>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      )}

      </section>
    </VerificationGuard>
  );
};

export default ProductsPage;

"use client";

import * as React from "react";
import { useState } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Edit3, X, Plus } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { useCategoriesHierarchy } from "@/lib/hooks/use-categories";
import { GENDER_OPTIONS, CONDITION_OPTIONS, Product, Bale, ProductOrBale } from "@/lib/types/products";
import { Category } from "@/lib/types/categories";
import { useUpdateProductBasicInfo } from "@/lib/hooks/use-products";

// Form validation schema for products
const productInfoSchema = z.object({
  name: z.string().min(1, "Product name is required"),
  brand: z.string().min(1, "Brand is required"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  basePrice: z.coerce.number().min(0.01, "Base price must be greater than 0"),
  category: z.string().min(1, "Category is required"),
  gender: z.enum(['Male', 'Female', 'Unisex'], {
    required_error: "Please select a gender",
  }),
  highlights: z.array(z.string()).min(1, "At least one highlight is required"),
  tags: z.array(z.string()).min(1, "At least one tag is required"),
});

// Form validation schema for bales
const baleInfoSchema = z.object({
  name: z.string().min(1, "Bale name is required"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  basePrice: z.coerce.number().min(0.01, "Base price must be greater than 0"),
  country: z.string().min(1, "Country is required"),
  totalItems: z.coerce.number().min(1, "Total items must be at least 1"),
  weight: z.coerce.number().min(0.1, "Weight must be greater than 0"),
  condition: z.enum(['Good', 'Fair', 'Excellent'], {
    required_error: "Please select a condition",
  }),
  highlights: z.array(z.string()).min(1, "At least one highlight is required"),
  tags: z.array(z.string()).min(1, "At least one tag is required"),
});

type ProductInfoFormData = z.infer<typeof productInfoSchema>;
type BaleInfoFormData = z.infer<typeof baleInfoSchema>;

// Helper component for displaying details
const DetailItem = ({ label, value }: { label: string; value: string | number }) => (
  <div className="text-sm">
    <p className="text-gray-500">{label}</p>
    <p className="text-gray-900">{value || "Not specified"}</p>
  </div>
);

interface ProductInfoSectionProps {
  product: ProductOrBale;
}

const ProductInfoSection: React.FC<ProductInfoSectionProps> = ({ product }) => {
  const [isEditing, setIsEditing] = useState(false);
  const { categories, isLoading: categoriesLoading } = useCategoriesHierarchy();

  // Local state for UI
  const [isModalOpen, setIsModalOpen] = React.useState(false);
  const [newHighlight, setNewHighlight] = React.useState("");
  const [newTag, setNewTag] = React.useState("");

  // API mutation
  const updateProductMutation = useUpdateProductBasicInfo();

  // Category navigation state
  const [currentCategories, setCurrentCategories] = React.useState<Category[]>([]);
  const [breadcrumb, setBreadcrumb] = React.useState<string[]>([]);
  const [searchTerm, setSearchTerm] = React.useState("");

  // Form setup - handle both product and bale types
  const isProduct = product.type === 'product';
  const productData = product as Product;
  const baleData = product as Bale;

  const form = useForm<ProductInfoFormData | BaleInfoFormData>({
    resolver: zodResolver(isProduct ? productInfoSchema : baleInfoSchema),
    defaultValues: isProduct ? {
      name: product.name || "",
      brand: productData.brand || "",
      description: product.description || "",
      basePrice: product.basePrice || 0,
      category: productData.category?._id || "",
      gender: productData.gender || "Unisex",
      highlights: product.highlights || [],
      tags: product.tags || [],
    } : {
      name: product.name || "",
      description: product.description || "",
      basePrice: product.basePrice || 0,
      country: baleData.country || "",
      totalItems: baleData.totalItems || 1,
      weight: baleData.weight || 0,
      condition: baleData.condition || "Good",
      highlights: product.highlights || [],
      tags: product.tags || [],
    },
  });

  const { control, handleSubmit, watch, setValue, formState: { errors } } = form;



  type FlattenedCategory = {
    _id: string;
    name: string;
    path: string;
  };

  // Recursive function to get all categories and their paths
  const flattenCategories = (categories: Category[], path: string[] = []): FlattenedCategory[] => {
    return categories.flatMap((category) => {
      const currentPath = [...path, category.name];

      if (category.immediateChildren && category.immediateChildren.length > 0) {
        return flattenCategories(category.immediateChildren, currentPath);
      }

      return [{ _id: category._id, name: category.name, path: currentPath.join(" > ") }];
    });
  };

  const allCategories = flattenCategories(categories || []);

  // Initialize categories when they load
  React.useEffect(() => {
    if (categories && categories.length > 0 && currentCategories.length === 0) {
      setCurrentCategories(categories);
    }
  }, [categories, currentCategories.length]);

  // Categories functions
  const openCategoryModal = () => {
    setSearchTerm("");
    setCurrentCategories(categories || []);
    setBreadcrumb([]);
    setIsModalOpen(true);
  };

  const selectCategory = (categoryId: string, categoryName: string) => {
    setValue('category', categoryId);
    setIsModalOpen(false);
  };

  const goDeeper = (category: Category) => {
    setBreadcrumb((prev) => [...prev, category.name]);
    setCurrentCategories(category.immediateChildren || []);
  };

  const goBack = () => {
    const updatedBreadcrumb = [...breadcrumb];
    updatedBreadcrumb.pop();
    setBreadcrumb(updatedBreadcrumb);

    let newCurrent = categories || [];
    for (const step of updatedBreadcrumb) {
      newCurrent = newCurrent.find((cat) => cat.name === step)?.immediateChildren || [];
    }
    setCurrentCategories(newCurrent);
  };

  const filteredCategories = searchTerm
    ? allCategories.filter((cat) =>
        cat.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : currentCategories;

  // Type guard to distinguish between search results and category objects
  function isFlattenedCategory(cat: any): cat is FlattenedCategory {
    return "path" in cat;
  }

  // Handle highlights
  const addHighlight = () => {
    if (newHighlight.trim()) {
      const currentHighlights = watch('highlights') || [];
      setValue('highlights', [...currentHighlights, newHighlight.trim()]);
      setNewHighlight("");
    }
  };

  const removeHighlight = (index: number) => {
    const currentHighlights = watch('highlights') || [];
    setValue('highlights', currentHighlights.filter((_, i) => i !== index));
  };

  // Handle tags
  const addTag = () => {
    if (newTag.trim()) {
      const currentTags = watch('tags') || [];
      setValue('tags', [...currentTags, newTag.trim()]);
      setNewTag("");
    }
  };

  const removeTag = (index: number) => {
    const currentTags = watch('tags') || [];
    setValue('tags', currentTags.filter((_, i) => i !== index));
  };



  const toggleEdit = () => setIsEditing(!isEditing);

  const onSubmit = async (data: ProductInfoFormData) => {
    try {
      await updateProductMutation.mutateAsync({
        productId: product._id,
        basicInfo: {
          name: data.name,
          brand: data.brand,
          description: data.description,
          gender: data.gender,
          basePrice: data.basePrice,
          highlights: data.highlights,
          tags: data.tags,
        },
      });
      toggleEdit();
    } catch (error) {
      // Error is handled by the mutation hook
      console.error('Product info update failed:', error);
    }
  };

  if (!isEditing) {
    // View Mode
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-semibold text-gray-800">
            {isProduct ? 'Product' : 'Bale'} Information
          </h3>
          <Button variant="ghost" onClick={toggleEdit}>
            <Edit3 className="h-5 w-5" />
          </Button>
        </div>

        <section className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <DetailItem label={isProduct ? "Product Name" : "Bale Name"} value={product.name} />
            {isProduct ? (
              <>
                <DetailItem label="Brand" value={productData.brand} />
                <DetailItem label="Gender" value={productData.gender} />
                <DetailItem label="Base Price" value={`GHS ${product.basePrice}`} />
              </>
            ) : (
              <>
                <DetailItem label="Country" value={baleData.country} />
                <DetailItem label="Total Items" value={baleData.totalItems} />
                <DetailItem label="Weight (kg)" value={baleData.weight} />
                <DetailItem label="Condition" value={baleData.condition} />
                <DetailItem label="Base Price" value={`GHS ${product.basePrice}`} />
              </>
            )}
          </div>

          {isProduct && <DetailItem label="Category" value={productData.category?.name} />}

          {!isProduct && baleData.dimensions && (
            <div className="text-sm">
              <p className="text-gray-500 mb-2">Dimensions (cm)</p>
              <div className="bg-gray-50 p-3 rounded-lg">
                <p className="text-gray-900">
                  {baleData.dimensions.length} × {baleData.dimensions.width} × {baleData.dimensions.height}
                </p>
              </div>
            </div>
          )}

          <div className="text-sm">
            <p className="text-gray-500 mb-2">Description</p>
            {product.description ? (
              <div className="bg-gray-50 p-3 rounded-lg">
                <p className="text-gray-900">{product.description}</p>
              </div>
            ) : (
              <p className="text-gray-400 italic">No description available</p>
            )}
          </div>

          <div className="text-sm">
            <p className="text-gray-500 mb-2">Highlights</p>
            {product.highlights && product.highlights.length > 0 ? (
              <div className="flex flex-wrap gap-2">
                {product.highlights.map((highlight: string, index: number) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {highlight}
                  </Badge>
                ))}
              </div>
            ) : (
              <p className="text-gray-400 italic">No highlights available</p>
            )}
          </div>

          <div className="text-sm">
            <p className="text-gray-500 mb-2">Tags</p>
            {product.tags && product.tags.length > 0 ? (
              <div className="flex flex-wrap gap-2">
                {product.tags.map((tag: string, index: number) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            ) : (
              <p className="text-gray-400 italic">No tags available</p>
            )}
          </div>
        </section>
      </div>
    );
  }

  // Edit Mode - Type-aware form
  return (
    <div className="w-full">
      <form onSubmit={handleSubmit(onSubmit)} className="bg-white mt-2 px-4 py-6 rounded-lg shadow-sm">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-lg font-bold">
            {isProduct ? 'Product' : 'Bale'} Information
          </h2>
          <Button variant="ghost" onClick={toggleEdit}>
            <X className="h-5 w-5" />
          </Button>
        </div>
        


        {/* Name */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-900 mb-1">
            {isProduct ? 'Product' : 'Bale'} Name <span className="text-red-500">*</span>
          </label>
          <Controller
            name="name"
            control={control}
            render={({ field }) => (
              <Input
                {...field}
                placeholder={`Enter ${isProduct ? 'product' : 'bale'} name`}
                className={errors.name ? 'border-red-300' : ''}
              />
            )}
          />
          {errors.name && (
            <p className="text-xs text-red-600 mt-1">{errors.name.message}</p>
          )}
        </div>

        {/* Type-specific fields */}
        {isProduct ? (
          // Product-specific fields
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-900 mb-1">
                Brand <span className="text-red-500">*</span>
              </label>
              <Controller
                name="brand"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    placeholder="Enter brand name"
                    className={errors.brand ? 'border-red-300' : ''}
                  />
                )}
              />
              {errors.brand && (
                <p className="text-xs text-red-600 mt-1">{errors.brand.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-900 mb-1">
                Gender <span className="text-red-500">*</span>
              </label>
              <Controller
                name="gender"
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger className={errors.gender ? 'border-red-300' : ''}>
                      <SelectValue placeholder="Select gender" />
                    </SelectTrigger>
                    <SelectContent>
                      {GENDER_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.gender && (
                <p className="text-xs text-red-600 mt-1">{errors.gender.message}</p>
              )}
            </div>
          </div>
        ) : (
          // Bale-specific fields
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-900 mb-1">
                Country <span className="text-red-500">*</span>
              </label>
              <Controller
                name="country"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    placeholder="e.g., Ghana, Nigeria, UK"
                    className={errors.country ? 'border-red-300' : ''}
                  />
                )}
              />
              {errors.country && (
                <p className="text-xs text-red-600 mt-1">{errors.country.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-900 mb-1">
                Total Items <span className="text-red-500">*</span>
              </label>
              <Controller
                name="totalItems"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="number"
                    min="1"
                    placeholder="e.g., 50"
                    className={errors.totalItems ? 'border-red-300' : ''}
                  />
                )}
              />
              {errors.totalItems && (
                <p className="text-xs text-red-600 mt-1">{errors.totalItems.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-900 mb-1">
                Weight (kg) <span className="text-red-500">*</span>
              </label>
              <Controller
                name="weight"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="number"
                    step="0.1"
                    min="0.1"
                    placeholder="e.g., 25.5"
                    className={errors.weight ? 'border-red-300' : ''}
                  />
                )}
              />
              {errors.weight && (
                <p className="text-xs text-red-600 mt-1">{errors.weight.message}</p>
              )}
            </div>
          </div>
        )}

        {/* Condition field for bales */}
        {!isProduct && (
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-900 mb-1">
              Condition <span className="text-red-500">*</span>
            </label>
            <Controller
              name="condition"
              control={control}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger className={errors.condition ? 'border-red-300' : ''}>
                    <SelectValue placeholder="Select condition" />
                  </SelectTrigger>
                  <SelectContent>
                    {CONDITION_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
            {errors.condition && (
              <p className="text-xs text-red-600 mt-1">{errors.condition.message}</p>
            )}
          </div>
        )}

        {/* Base Price and Category */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">
              Base Price (GHS) <span className="text-red-500">*</span>
            </label>
            <Controller
              name="basePrice"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="0.00"
                  className={errors.basePrice ? 'border-red-300' : ''}
                />
              )}
            />
            {errors.basePrice && (
              <p className="text-xs text-red-600 mt-1">{errors.basePrice.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">
              Category <span className="text-red-500">*</span>
            </label>
            <Button
              type="button"
              variant="outline"
              className="w-full justify-start"
              onClick={openCategoryModal}
            >
              {product.category?.name || "Select Category"}
            </Button>
          </div>
        </div>

        {/* Category Selection Modal */}
        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
          <DialogContent className="w-full max-w-[90%] max-h-[90vh] overflow-y-auto rounded-lg">
            <DialogHeader>
              <DialogTitle>Select Category</DialogTitle>
            </DialogHeader>

            {categoriesLoading ? (
              <div className="text-center py-4">Loading categories...</div>
            ) : (
              <>
                {breadcrumb.length > 0 && !searchTerm && (
                  <Button variant="ghost" onClick={goBack} className="mb-2">
                    ← Back
                  </Button>
                )}

                <Input
                  placeholder="Search categories..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="mb-4"
                />

                <ul className="space-y-2">
                  {filteredCategories.map((cat, index) => (
                    <li key={`${cat.name}-${index}`}>
                      {isFlattenedCategory(cat) ? (
                        // Search result view
                        <Button
                          variant="outline"
                          className="w-full text-left justify-start"
                          onClick={() => selectCategory(cat._id, cat.name)}
                        >
                          <div>
                            <div>{cat.name}</div>
                            <div className="text-xs text-gray-500">under {cat.path}</div>
                          </div>
                        </Button>
                      ) : cat.immediateChildren && cat.immediateChildren.length > 0 ? (
                        // Navigable category view
                        <Button
                          variant="ghost"
                          className="w-full text-left justify-start"
                          onClick={() => goDeeper(cat)}
                        >
                          {cat.name} →
                        </Button>
                      ) : (
                        // Selectable category without subcategories
                        <Button
                          variant="outline"
                          className="w-full text-left justify-start"
                          onClick={() => selectCategory(cat._id, cat.name)}
                        >
                          {cat.name}
                        </Button>
                      )}
                    </li>
                  ))}
                </ul>
              </>
            )}
          </DialogContent>
        </Dialog>

        {/* Description */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-900 mb-1">
            Description <span className="text-red-500">*</span>
          </label>
          <Controller
            name="description"
            control={control}
            render={({ field }) => (
              <Textarea
                {...field}
                placeholder="Describe your product in detail..."
                rows={4}
                className={errors.description ? 'border-red-300' : ''}
              />
            )}
          />
          {errors.description && (
            <p className="text-xs text-red-600 mt-1">{errors.description.message}</p>
          )}
        </div>

        {/* Highlights */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-900 mb-1">
            Product Highlights <span className="text-red-500">*</span>
          </label>
          <div className="flex gap-2 mb-2">
            <Input
              value={newHighlight}
              onChange={(e) => setNewHighlight(e.target.value)}
              placeholder="Add a highlight..."
              onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addHighlight())}
            />
            <Button type="button" onClick={addHighlight} size="sm">
              <Plus className="w-4 h-4" />
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {watch('highlights')?.map((highlight: string, index: number) => (
              <Badge key={index} variant="secondary" className="flex items-center gap-1">
                {highlight}
                <button
                  type="button"
                  onClick={() => removeHighlight(index)}
                  className="ml-1 hover:text-red-500"
                >
                  <X className="w-3 h-3" />
                </button>
              </Badge>
            ))}
          </div>
          {errors.highlights && (
            <p className="text-xs text-red-600 mt-1">{errors.highlights.message}</p>
          )}
        </div>

        {/* Tags */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-900 mb-1">
            Tags <span className="text-red-500">*</span>
          </label>
          <div className="flex gap-2 mb-2">
            <Input
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              placeholder="Add a tag..."
              onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
            />
            <Button type="button" onClick={addTag} size="sm">
              <Plus className="w-4 h-4" />
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {watch('tags')?.map((tag: string, index: number) => (
              <Badge key={index} variant="outline" className="flex items-center gap-1">
                {tag}
                <button
                  type="button"
                  onClick={() => removeTag(index)}
                  className="ml-1 hover:text-red-500"
                >
                  <X className="w-3 h-3" />
                </button>
              </Badge>
            ))}
          </div>
          {errors.tags && (
            <p className="text-xs text-red-600 mt-1">{errors.tags.message}</p>
          )}
        </div>

        {/* Submit Button */}
        <div className="flex justify-end mt-8">
          <Button type="submit" className="px-8">
            Save Changes
          </Button>
        </div>
      </form>
    </div>
  );
};

export default ProductInfoSection;

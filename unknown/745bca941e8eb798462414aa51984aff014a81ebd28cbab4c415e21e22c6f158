'use client';

import React from 'react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Package, Archive } from 'lucide-react';
import { useProductForm } from '@/lib/contexts/ProductFormContext';
import { ProductType, PRODUCT_TYPE_OPTIONS } from '@/lib/types/products';

const typeSelectionSchema = z.object({
  type: z.enum(['product', 'bale'], {
    required_error: 'Please select a type',
  }),
});

type TypeSelectionFormData = z.infer<typeof typeSelectionSchema>;

interface TypeSelectionFormProps {
  onNext: () => void;
}

const TypeSelectionForm: React.FC<TypeSelectionFormProps> = ({ onNext }) => {
  const { currentType, setCurrentType } = useProductForm();

  const form = useForm<TypeSelectionFormData>({
    resolver: zodResolver(typeSelectionSchema),
    defaultValues: {
      type: currentType,
    },
  });

  const { control, handleSubmit, formState: { errors } } = form;

  const onSubmit = (data: TypeSelectionFormData) => {
    setCurrentType(data.type);
    onNext();
  };

  return (
    <div className="w-full max-w-2xl mx-auto">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          What would you like to create?
        </h1>
        <p className="text-gray-600">
          Choose the type of item you want to add to your inventory.
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <Controller
          name="type"
          control={control}
          render={({ field }) => (
            <RadioGroup
              value={field.value}
              onValueChange={field.onChange}
              className="grid grid-cols-1 md:grid-cols-2 gap-4"
            >
              <div>
                <RadioGroupItem value="product" id="product" className="peer sr-only" />
                <Label
                  htmlFor="product"
                  className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-6 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary cursor-pointer transition-colors"
                >
                  <Package className="mb-3 h-8 w-8" />
                  <div className="text-center">
                    <div className="font-semibold">Product</div>
                    <div className="text-sm text-muted-foreground mt-1">
                      Individual clothing items with specific sizes, colors, and variations
                    </div>
                  </div>
                </Label>
              </div>

              <div>
                <RadioGroupItem value="bale" id="bale" className="peer sr-only" />
                <Label
                  htmlFor="bale"
                  className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-6 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary cursor-pointer transition-colors"
                >
                  <Archive className="mb-3 h-8 w-8" />
                  <div className="text-center">
                    <div className="font-semibold">Bale</div>
                    <div className="text-sm text-muted-foreground mt-1">
                      Bulk clothing packages with weight, quantity, and condition details
                    </div>
                  </div>
                </Label>
              </div>
            </RadioGroup>
          )}
        />

        {errors.type && (
          <p className="text-sm text-red-600">{errors.type.message}</p>
        )}

        <div className="flex justify-end">
          <Button type="submit" className="px-8">
            Continue
          </Button>
        </div>
      </form>

      {/* Information Cards */}
      <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg">
              <Package className="h-5 w-5" />
              Products
            </CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription>
              Perfect for individual clothing items like dresses, shirts, pants, etc. 
              You can specify different colors, sizes, and set individual pricing for each variation.
            </CardDescription>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg">
              <Archive className="h-5 w-5" />
              Bales
            </CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription>
              Ideal for bulk clothing packages. Specify total weight, number of items, 
              condition, and country of origin. Great for wholesale operations.
            </CardDescription>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default TypeSelectionForm;

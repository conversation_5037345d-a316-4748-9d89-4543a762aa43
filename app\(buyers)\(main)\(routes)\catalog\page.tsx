"use client";

import { useSearchParams } from "next/navigation";
import GeneralSortAndFilter from "../../_components/GeneralSortAndFilter";
import ProductList from "@/components/ui/custom/ProductsList";

const CatalogPageContent = () => {
  const searchParams = useSearchParams();

  const searchQuery = searchParams.get("q");
  const category = searchParams.get("category");
  const brands = searchParams.get("brands");
  const sort = searchParams.get("sort");
  const minPrice = searchParams.get("minPrice");
  const maxPrice = searchParams.get("maxPrice");
  const sizes = searchParams.get("sizes");
  const gender = searchParams.get("gender");

  // The header adapts based on filters
  let headerText = "All Products";
  let subHeaderText = "";

  // Check if **any filter is active**
  const hasFilters =
    searchQuery ||
    category ||
    brands ||
    sort ||
    minPrice ||
    maxPrice ||
    sizes ||
    gender;

  if (hasFilters) {
    if (searchQuery) {
      headerText = `Search Results for "${searchQuery}"`;
    } else {
      headerText = "Filtered Products";
      // Build a descriptive sub-header
      const activeFilters = [];
      if (category) activeFilters.push(`Category: ${category}`);
      if (brands) activeFilters.push(`Brand: ${brands}`);
      if (gender) activeFilters.push(`Gender: ${gender}`);
      if (minPrice || maxPrice) {
        const priceFilter = minPrice && maxPrice
          ? `Price: GH₵${minPrice} - GH₵${maxPrice}`
          : minPrice
          ? `Price: From GH₵${minPrice}`
          : `Price: Up to GH₵${maxPrice}`;
        activeFilters.push(priceFilter);
      }
      if (sizes) activeFilters.push(`Size: ${sizes}`);
      if (sort) activeFilters.push(`Sorted by: ${sort}`);

      subHeaderText = activeFilters.slice(0, 3).join(" • ");
      if (activeFilters.length > 3) {
        subHeaderText += ` • +${activeFilters.length - 3} more`;
      }
    }
  }

  return (
    <section className="w-full mt-16">
      <GeneralSortAndFilter />

      <div className="mb-6">
        <h1 className="text-gray-700 font-semibold mb-1">
          {headerText}
        </h1>
        {subHeaderText && (
          <p className="text-gray-500 text-sm">
            {subHeaderText}
          </p>
        )}
      </div>

      <ProductList />
    </section>
  );
};

import React, { Suspense } from 'react'
import LottieLoader from '@/components/ui/LottieLoader';

const CatalogPage = () => {
  return (
    <Suspense fallback={
      <section className="w-full mt-16 flex items-center justify-center py-12">
        <LottieLoader
          size="sm"
          text="Loading catalog..."
          textSize="md"
          centered={true}
        />
      </section>
    }>
        <CatalogPageContent/>
    </Suspense>
  )
}


export default CatalogPage;

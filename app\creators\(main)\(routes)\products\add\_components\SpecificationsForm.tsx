"use client";

import React, { useState } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { X, Plus, ChevronLeft } from "lucide-react";
import { useProductForm } from "@/lib/contexts/ProductFormContext"
import BaleSpecificationsForm from "./BaleSpecificationsForm";
import {
  FIT_TYPE_OPTIONS,
  MAIN_MATERIAL_OPTIONS,
  DRESS_STYLE_OPTIONS,
  PANT_TYPE_OPTIONS,
  SKIRT_TYPE_OPTIONS,
  PATTERN_OPTIONS,
  CLOSURE_OPTIONS,
  NECKLINE_OPTIONS,
  SLEEVE_LENGTH_OPTIONS,
  WAISTLINE_OPTIONS,
  HEMLINE_OPTIONS
} from "@/lib/types/products";
import { toast } from "@/hooks/use-toast";

// Form validation schema
const specificationsSchema = z.object({
  mainMaterial: z.string().optional(),
  dressStyle: z.string().optional(),
  pantType: z.string().optional(),
  skirtType: z.string().optional(),
  mensPantSize: z.string().optional(),
  fitType: z.enum(['Slim', 'Regular', 'Loose', 'Oversized', 'Tailored', 'Skinny', 'Straight', 'Relaxed']).optional(),
  pattern: z.string().optional(),
  closure: z.string().optional(),
  neckline: z.string().optional(),
  sleeveLength: z.string().optional(),
  waistline: z.string().optional(),
  hemline: z.string().optional(),
});

type SpecificationsFormData = z.infer<typeof specificationsSchema>;

interface SpecificationsFormProps {
  onNext: () => void;
  onBack: () => void;
}

export default function SpecificationsForm({ onNext, onBack }: SpecificationsFormProps) {
  const { currentType, specifications, updateSpecifications } = useProductForm()

  // If current type is bale, use the BaleSpecificationsForm
  if (currentType === 'bale') {
    return <BaleSpecificationsForm onNext={onNext} onBack={onBack} />;
  };

  // Form setup
  const form = useForm<SpecificationsFormData>({
    resolver: zodResolver(specificationsSchema),
    defaultValues: {
      mainMaterial: specifications.mainMaterial || "",
      dressStyle: specifications.dressStyle || "",
      pantType: specifications.pantType || "",
      skirtType: specifications.skirtType || "",
      mensPantSize: specifications.mensPantSize || "",
      fitType: specifications.fitType || undefined,
      pattern: specifications.pattern || "",
      closure: specifications.closure || "",
      neckline: specifications.neckline || "",
      sleeveLength: specifications.sleeveLength || "",
      waistline: specifications.waistline || "",
      hemline: specifications.hemline || "",
    },
  });

  const { control, handleSubmit, formState: { errors } } = form;

  const onSubmit = (data: SpecificationsFormData) => {
    // Update context with final data
    updateSpecifications(data);

    // Navigate to next step
    onNext();
  };

  return (
    <div className="w-full">
      <form onSubmit={handleSubmit(onSubmit)} className="bg-white mt-2 px-4 py-6 rounded-lg shadow-sm">
        <h2 className="text-lg font-bold mb-6">Product Specifications</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Main Material */}
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">Main Material</label>
            <Controller
              name="mainMaterial"
              control={control}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select main material" />
                  </SelectTrigger>
                  <SelectContent>
                    {MAIN_MATERIAL_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>

          {/* Fit Type */}
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">Fit Type</label>
            <Controller
              name="fitType"
              control={control}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select fit type" />
                  </SelectTrigger>
                  <SelectContent>
                    {FIT_TYPE_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>

          {/* Dress Style */}
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">Dress Style</label>
            <Controller
              name="dressStyle"
              control={control}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select dress style" />
                  </SelectTrigger>
                  <SelectContent>
                    {DRESS_STYLE_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>

          {/* Pant Type */}
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">Pant Type</label>
            <Controller
              name="pantType"
              control={control}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select pant type" />
                  </SelectTrigger>
                  <SelectContent>
                    {PANT_TYPE_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>

          {/* Skirt Type */}
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">Skirt Type</label>
            <Controller
              name="skirtType"
              control={control}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select skirt type" />
                  </SelectTrigger>
                  <SelectContent>
                    {SKIRT_TYPE_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>

          {/* Men's Pant Size */}
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">Men's Pant Size</label>
            <Controller
              name="mensPantSize"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  placeholder="e.g., 32x34, 30x32"
                />
              )}
            />
          </div>

          {/* Pattern */}
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">Pattern</label>
            <Controller
              name="pattern"
              control={control}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select pattern" />
                  </SelectTrigger>
                  <SelectContent>
                    {PATTERN_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>

          {/* Closure */}
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">Closure</label>
            <Controller
              name="closure"
              control={control}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select closure type" />
                  </SelectTrigger>
                  <SelectContent>
                    {CLOSURE_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>
        </div>

        {/* Additional Specifications */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Neckline */}
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">Neckline</label>
            <Controller
              name="neckline"
              control={control}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select neckline" />
                  </SelectTrigger>
                  <SelectContent>
                    {NECKLINE_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>

          {/* Sleeve Length */}
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">Sleeve Length</label>
            <Controller
              name="sleeveLength"
              control={control}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select sleeve length" />
                  </SelectTrigger>
                  <SelectContent>
                    {SLEEVE_LENGTH_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>

          {/* Waistline */}
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">Waistline</label>
            <Controller
              name="waistline"
              control={control}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select waistline" />
                  </SelectTrigger>
                  <SelectContent>
                    {WAISTLINE_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>

          {/* Hemline */}
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">Hemline</label>
            <Controller
              name="hemline"
              control={control}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select hemline" />
                  </SelectTrigger>
                  <SelectContent>
                    {HEMLINE_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>
        </div>



        {/* Navigation Buttons */}
        <div className="flex justify-between mt-8">
          <Button 
            type="button" 
            variant="outline" 
            onClick={onBack}
            className="flex items-center gap-2"
          >
            <ChevronLeft className="w-4 h-4" />
            Back
          </Button>
          <Button type="submit" className="px-8">
            Next: Variations
          </Button>
        </div>
      </form>
    </div>
  );
}

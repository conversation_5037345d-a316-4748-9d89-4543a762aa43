"use client"
import { useRouter } from 'next/navigation';
import React from 'react';

interface Product {
    id: string;
    image: string;
    name: string;
    price: number;
    discount?: number;
    itemsLeft?: number;
  }

  interface ProductCardProps {
    product: Product;
    cardType: 'flashy' | 'normal' | 'withAddToCart';
  }


  const ProductCard: React.FC<ProductCardProps> = ({ product, cardType }) => {
    const { id, image, name, price, discount, itemsLeft } = product;
    const router = useRouter();

    const onClick = () => {
      router.push(`/items/${id}`)
    }

    return (
      <div className="relative bg-white shadow-md rounded-lg overflow-hidden w-full">
        {/* Product Image */}
        <img src={image} alt={name} className="w-full h-48 object-cover" onClick={onClick} />

        {/* Discount Badge */}
        {discount && (
          <div className="absolute top-2 right-2 bg-white/60 text-primary text-xs font-bold px-2 py-1 rounded">
            -{discount}%
          </div>
        )}

        {/* Product Details */}
        <div className="p-2" onClick={onClick}>
          <h2 className="text-sm truncate">{name}</h2>
          <div className="flex gap-1 items-center flex-wrap">
          {discount ? (
            <>
              <p className="text-lg font-semibold">GH₵{price}</p>
              <p className="text-sm text-gray-500 line-through">
              GH₵ {(price / (1 - discount / 100)).toFixed(2)}
            </p>
            </>
          ) : (
            <p className="text-lg font-bold text-gray-800">GH₵ {price}</p>
          )}
          </div>


          {cardType !== 'normal' &&  cardType !== 'withAddToCart' && itemsLeft !== undefined && (
            <p className={`text-xs font-semibold ${itemsLeft > 0 ? 'text-gray-500' : 'text-red-500'}`}>
              {itemsLeft > 0 ? `${itemsLeft} items left` : 'Out of stock'}
            </p>
          )}
        </div>

        <div className="p-2">
          {/* Conditional Add to Cart Button */}
          {cardType === 'withAddToCart' && itemsLeft !== undefined && itemsLeft > 0 && (
            <button className="mt-2 w-full bg-primary text-white text-sm font-bold py-1 px-2 rounded hover:bg-primary/90 transition">
              Add to Cart
            </button>
          )}
        </div>

      </div>
    );
  };

  export default ProductCard;